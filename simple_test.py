"""
Simple test to verify our setup works
"""

import os
import torch
from PIL import Image
import numpy as np

def test_basic_setup():
    """Test basic functionality without heavy models"""
    print("🧪 Testing basic setup...")
    
    # Test PyTorch
    print(f"PyTorch version: {torch.__version__}")
    print(f"CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"CUDA device: {torch.cuda.get_device_name(0)}")
    
    # Test PIL
    print("✅ PIL working")
    
    # Test image loading
    test_images = [
        "style_photos/before.png",
        "1.png",
        "2.png"
    ]
    
    found_images = []
    for img_path in test_images:
        if os.path.exists(img_path):
            try:
                img = Image.open(img_path)
                print(f"✅ Loaded {img_path}: {img.size}")
                found_images.append(img_path)
            except Exception as e:
                print(f"❌ Error loading {img_path}: {e}")
        else:
            print(f"⚠️ Image not found: {img_path}")
    
    return found_images

def test_image_processing(image_path):
    """Test basic image processing"""
    print(f"\n🎨 Testing image processing with {image_path}")
    
    try:
        # Load image
        img = Image.open(image_path).convert('RGB')
        print(f"Original size: {img.size}")
        
        # Resize to reasonable size
        max_size = 512
        w, h = img.size
        if w > h:
            new_w = max_size
            new_h = int(h * max_size / w)
        else:
            new_h = max_size
            new_w = int(w * max_size / h)
        
        # Ensure dimensions are multiples of 8
        new_w = (new_w // 8) * 8
        new_h = (new_h // 8) * 8
        
        resized_img = img.resize((new_w, new_h), Image.Resampling.LANCZOS)
        print(f"Resized to: {resized_img.size}")
        
        # Apply some basic watercolor-like effects
        from PIL import ImageFilter, ImageEnhance
        
        # Slight blur for softer look
        soft_img = resized_img.filter(ImageFilter.GaussianBlur(radius=0.5))
        
        # Reduce saturation for pastel effect
        enhancer = ImageEnhance.Color(soft_img)
        pastel_img = enhancer.enhance(0.8)
        
        # Save test result
        output_path = f"test_processed_{os.path.basename(image_path)}"
        pastel_img.save(output_path)
        print(f"✅ Saved processed image: {output_path}")
        
        return output_path
        
    except Exception as e:
        print(f"❌ Error processing image: {e}")
        return None

def test_diffusers_import():
    """Test if diffusers can be imported"""
    print("\n📦 Testing diffusers import...")
    
    try:
        from diffusers import StableDiffusionImg2ImgPipeline
        print("✅ Diffusers imported successfully")
        return True
    except Exception as e:
        print(f"❌ Error importing diffusers: {e}")
        return False

def main():
    print("🚀 Running simple tests...\n")
    
    # Test basic setup
    found_images = test_basic_setup()
    
    # Test diffusers import
    diffusers_ok = test_diffusers_import()
    
    # Test image processing if we have images
    if found_images:
        test_image_processing(found_images[0])
    
    print("\n📊 Test Summary:")
    print(f"✅ Basic setup: OK")
    print(f"{'✅' if diffusers_ok else '❌'} Diffusers: {'OK' if diffusers_ok else 'FAILED'}")
    print(f"✅ Image processing: {'OK' if found_images else 'No test images found'}")
    
    if diffusers_ok and found_images:
        print("\n🎉 Ready to try the watercolor pipeline!")
        print("Run: python watercolor_pipeline.py")
    else:
        print("\n⚠️ Some components need attention before running the full pipeline")

if __name__ == "__main__":
    main()
