# Architecture Optimization Plan

## Recommended Architecture: Firebase-Only Approach

### Benefits:
1. Single storage system (simpler)
2. Direct integration with Firebase Functions
3. No cross-service authentication needed
4. Better performance (no intermediate downloads)

### Optimized Flow:

1. **Upload Stage**
   - User uploads to Firebase Storage path: `temp/{sessionId}/{filename}`
   - Return signed URLs (expire in 1 hour)
   - Store URLs in Stripe checkout metadata

2. **Payment Stage**
   - Create Stripe checkout with file URLs
   - No file data in database (just references)

3. **Webhook Processing**
   - Move files from `temp/` to `orders/{orderId}/`
   - Process images directly in Firebase
   - Delete temp files after processing

4. **Cleanup Job**
   - Cloud Scheduler runs daily
   - Deletes temp files older than 24 hours

### Implementation Changes Needed:

1. Update `/api/upload-temp` to use Firebase Storage
2. Add Firebase Storage CORS configuration
3. Create cleanup Cloud Function
4. Update webhook to move files instead of download/re-upload

### Storage Format:
- Keep as binary (JPEG/PNG) for efficiency
- Only convert to base64 for email embedding if needed

### Estimated Improvements:
- 50% faster upload processing
- 33% less storage used
- Simpler error handling
- Easier debugging

Would you like me to implement this optimized architecture?