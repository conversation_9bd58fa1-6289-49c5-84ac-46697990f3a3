# 🎨 Illustrations V2 - Photo to Art Transformation Service

Transform your precious memories into beautiful pencil and watercolor illustrations. This is a modern web application built with Next.js that provides an automated photo transformation service with Stripe payments and email delivery.

## ✨ Features

### 🖼️ Photo Transformation
- Convert photos to artistic pencil and watercolor illustrations
- High-resolution output suitable for printing
- Automated processing using AI/image processing
- Multiple style options available

### 💳 E-commerce Integration
- **Stripe Payments**: Secure payment processing
- **Flexible Pricing**: Pay-per-photo with bundle discounts
  - 1 photo: $9.99
  - 2-3 photos: $19.99 (save $10)
  - 4-5 photos: $29.99 (save $20)
- **No account required**: Simple checkout process

### 🚀 Modern Architecture
- **Next.js 15** with App Router and React 19
- **TypeScript** for type safety
- **Tailwind CSS** with Shadcn UI components
- **Firebase Functions** for serverless backend processing
- **Supabase** for database and storage
- **Framer Motion** for smooth animations

### 📧 Automated Delivery
- Email delivery of completed illustrations
- High-resolution files ready for printing
- Automated order tracking and notifications

## 🛠️ Tech Stack

### Frontend
- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS + Shadcn UI
- **UI Icons**: Lucide React - Modern, customizable icon library
- **Animations**: Framer Motion
- **Forms**: React Hook Form + Zod validation
- **File Upload**: React Dropzone

### Backend & Services
- **Serverless**: Firebase Functions
- **Database**: Supabase (PostgreSQL)
- **Storage**: File uploads and processing
- **Payments**: Stripe integration
- **Email**: Resend for transactional emails
- **Image Processing**: Sharp, PDFKit, Puppeteer

### Development Tools
- **AI Assistant**: Claude Code with MCP servers
- **Version Control**: GitHub with CI/CD
- **Development Environment**: WSL2 (Ubuntu)
- **Package Manager**: npm

### Infrastructure
- **Deployment**: Vercel (Frontend) + Firebase (Functions)
- **CI/CD**: GitHub Actions
- **Monitoring**: Built-in error tracking

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or pnpm
- Firebase CLI
- Stripe account
- Supabase project
- Claude Code (optional, for AI-assisted development)

### Key Dependencies
- **Next.js 15.3.3** - React framework with App Router
- **React 19.0.0** - UI library
- **TypeScript 5+** - Type safety
- **Tailwind CSS 3.4.17** - Utility-first CSS
- **Lucide React 0.511.0** - Modern icon library
- **Framer Motion 12.15.0** - Animation library
- **Stripe 18.3.0** - Payment processing
- **Supabase JS 2.49.9** - Database client

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd illustrations-v2
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create `.env.local` file:
   ```env
   # Stripe
   NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_...
   STRIPE_SECRET_KEY=sk_test_...
   STRIPE_WEBHOOK_SECRET=whsec_...

   # Supabase
   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJ...
   SUPABASE_SERVICE_ROLE_KEY=eyJ...

   # Resend Email
   RESEND_API_KEY=re_...

   # Firebase
   FIREBASE_PROJECT_ID=your-project-id
   ```

4. **Set up Firebase Functions**
   ```bash
   cd firebase/functions
   npm install
   firebase login
   firebase use your-project-id
   ```

5. **Initialize database**
   ```bash
   # Run the schema in Supabase dashboard
   # Or use Supabase CLI
   supabase db push
   ```

### Development

1. **Start the development server**
   ```bash
   npm run dev
   ```

2. **Start Firebase emulators** (in separate terminal)
   ```bash
   cd firebase/functions
   npm run serve
   ```

3. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### AI-Assisted Development (Optional)

This project is configured for enhanced development with Claude Code and MCP servers:

1. **Install Claude Code CLI**
   ```bash
   npm install -g @anthropic-ai/claude-code
   ```

2. **Available MCP Servers**
   - **Supabase**: Database operations and schema management
   - **Stripe**: Payment and product management
   - **GitHub**: Version control and CI/CD
   - **Context7**: Documentation and code examples
   - **Sequential Thinking**: Complex problem solving

3. **Using MCP in Claude Code**
   ```bash
   claude
   /mcp  # Check server status
   ```

   Example commands:
   - "Show me all orders from the last week"
   - "Create a new database migration for user profiles"
   - "Update the Stripe product pricing"
   - "Create a PR for the new feature branch"

## 🎨 UI/UX Design

### Component Library
The project uses **Shadcn UI** for consistent, accessible components built on Radix UI primitives. Components are customizable and follow the project's design system.

### Icon System
**Lucide React** provides the iconography throughout the application:
- 🎯 **Consistent Design**: All icons share a 24x24 viewBox
- 📦 **Optimized Bundle**: Tree-shakable imports for minimal size
- 🎨 **Customizable**: Easy to style with Tailwind classes
- 🔍 **Comprehensive Set**: Navigation, status, e-commerce, and feature icons

Common icons used:
- `ArrowRight`, `Menu`, `X` - Navigation
- `Sparkles`, `Zap`, `Heart` - Features
- `Check`, `CheckCircle`, `Star` - Status
- `Upload`, `CreditCard`, `Mail` - Actions

### Animations
**Framer Motion** powers smooth, performant animations:
- Hero section floating elements
- Card hover effects
- Page transitions
- Loading states

## 📁 Project Structure

```
illustrations-v2/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API routes
│   │   │   ├── create-checkout-session/
│   │   │   ├── stripe-webhook/
│   │   │   ├── upload/
│   │   │   └── send-illustration/
│   │   ├── create/            # Order creation flow
│   │   └── page.tsx           # Landing page
│   ├── components/            # React components
│   │   ├── ui/               # Shadcn UI components
│   │   ├── hero-section.tsx
│   │   ├── pricing-section.tsx
│   │   └── ...
│   └── lib/                  # Utilities and configurations
├── firebase/
│   └── functions/            # Serverless functions
│       ├── src/
│       │   ├── api/          # API endpoints
│       │   ├── processors/   # Image processing
│       │   ├── services/     # Business logic
│       │   └── webhooks/     # Payment webhooks
│       └── lib/              # Shared utilities
├── public/                   # Static assets
├── scripts/                  # Build and deployment scripts
└── CLAUDE.md                # AI assistant guidance
```

## 🔧 API Routes

- `POST /api/create-checkout-session` - Create Stripe payment session
- `POST /api/stripe-webhook` - Handle Stripe events
- `POST /api/upload` - Handle file uploads
- `POST /api/send-illustration` - Deliver completed illustrations
- `GET /api/order/[sessionId]` - Get order status

## 💳 Payment Flow

1. User uploads photos and selects package
2. Stripe checkout session created based on photo count
3. User completes payment
4. Webhook triggers Firebase function
5. Photos processed into illustrations
6. Completed illustrations emailed to customer

## 🎨 Image Processing

The service uses a combination of:
- **Sharp** for image optimization and format conversion
- **Custom algorithms** for artistic transformation
- **PDFKit** for generating print-ready formats
- **Puppeteer** for advanced rendering capabilities

## 📧 Email Templates

Automated emails include:
- Order confirmation
- Processing updates
- Delivery notifications
- Receipt and invoice

## 🚀 Deployment

### Frontend (Vercel)
```bash
# Connect to Vercel and deploy
vercel --prod
```

### Backend (Firebase)
```bash
cd firebase/functions
firebase deploy --only functions
```

### Database (Supabase)
```bash
# Push migrations
supabase db push

# Generate types
supabase gen types typescript --local > src/types/supabase.ts
```

### Environment Setup
- Configure production environment variables
- Set up Stripe live mode
- Configure production email templates
- Enable Supabase RLS policies

## 🔒 Security

- Stripe webhooks with signature verification
- Input validation with Zod schemas
- Rate limiting on API endpoints
- Secure file upload with type validation
- Environment variable protection
- Supabase Row Level Security (RLS)

## 📊 Analytics & Monitoring

- Built-in error tracking
- Payment success/failure monitoring
- Processing time analytics
- Customer satisfaction metrics

## 🧪 Testing

### Development Testing
- Test pages available at `/test` and `/test-order`
- Manual API endpoint testing
- Stripe test mode for payment flows

### Production Testing
- Comprehensive deployment checklist
- Staging environment validation
- Load testing for image processing

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

### Development Guidelines
- Follow TypeScript strict mode
- Use Zod for runtime validation
- Maintain consistent Tailwind CSS patterns
- Document complex business logic
- Test payment flows in Stripe test mode

## 📝 Current Status

The project is functionally complete with:
- ✅ Complete payment integration
- ✅ File upload and storage
- ✅ Order tracking system
- ✅ Email delivery
- ⚠️ Placeholder image processing (AI integration pending)
- ⚠️ Basic responsive design (needs polish)

## 🚨 Known Issues

- Effect MCP server may have connectivity issues in WSL
- Test endpoints need removal before production
- Mobile responsive design needs refinement

## 📄 License

This project is proprietary software. All rights reserved.

## 🆘 Support

For technical issues or questions:
- Check the FAQ section on the website
- Review API documentation
- Contact support team

---

Built with ❤️ using modern web technologies to transform memories into timeless art.
