"""
Minimal Watercolor Pipeline - CPU-friendly version
"""

import torch
import time
from PIL import Image, ImageEnhance
from diffusers import StableDiffusionImg2ImgPipeline
import os

class MinimalWatercolorPipeline:
    def __init__(self):
        self.pipeline = None
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        print(f"🎨 Initializing Minimal Watercolor Pipeline on {self.device}")
    
    def load_pipeline(self):
        """Load the Stable Diffusion pipeline"""
        if self.pipeline is not None:
            return True
            
        print("📦 Loading Stable Diffusion pipeline...")
        try:
            # Use SD 1.5 for faster loading and CPU compatibility
            model_id = "runwayml/stable-diffusion-v1-5"
            
            self.pipeline = StableDiffusionImg2ImgPipeline.from_pretrained(
                model_id,
                torch_dtype=torch.float32,  # Use float32 for CPU
                safety_checker=None,
                requires_safety_checker=False
            )
            
            # CPU optimizations
            self.pipeline.enable_attention_slicing()
            
            print("✅ Pipeline loaded successfully!")
            return True
            
        except Exception as e:
            print(f"❌ Error loading pipeline: {e}")
            return False
    
    def preprocess_image(self, image_path, max_size=512):
        """Load and preprocess image"""
        print(f"🖼️ Processing image: {image_path}")
        
        # Load image
        image = Image.open(image_path).convert('RGB')
        original_size = image.size
        print(f"   Original size: {original_size}")
        
        # Resize while maintaining aspect ratio
        w, h = image.size
        if w > h:
            new_w = max_size
            new_h = int(h * max_size / w)
        else:
            new_h = max_size
            new_w = int(w * max_size / h)
        
        # Ensure dimensions are multiples of 8 (required for SD)
        new_w = (new_w // 8) * 8
        new_h = (new_h // 8) * 8
        
        resized_image = image.resize((new_w, new_h), Image.Resampling.LANCZOS)
        print(f"   Resized to: {resized_image.size}")
        
        return resized_image
    
    def generate_watercolor(self, image_path, output_path=None, strength=0.75):
        """Generate watercolor version of the image"""
        
        # Load pipeline if not already loaded
        if not self.load_pipeline():
            return None
        
        # Preprocess image
        input_image = self.preprocess_image(image_path)
        
        # Watercolor prompt
        prompt = """watercolor painting, soft brush strokes, gentle colors, 
        artistic illustration, pastel tones, delicate washes, 
        romantic style, dreamy atmosphere, paper texture, 
        beautiful watercolor art, soft edges, flowing colors"""
        
        negative_prompt = """photorealistic, harsh lines, digital art, 
        cartoon, anime, sharp edges, high contrast, dark colors, 
        ugly, blurry, low quality, distorted"""
        
        print("🎨 Generating watercolor illustration...")
        print(f"   Strength: {strength}")
        print(f"   Device: {self.device}")
        
        start_time = time.time()
        
        try:
            # Generate image
            result = self.pipeline(
                prompt=prompt,
                negative_prompt=negative_prompt,
                image=input_image,
                num_inference_steps=15,  # Reduced for speed
                guidance_scale=7.5,
                strength=strength
            )
            
            generated_image = result.images[0]
            
            # Post-process for watercolor effect
            generated_image = self.post_process_watercolor(generated_image)
            
            # Save result
            if output_path is None:
                base_name = os.path.splitext(os.path.basename(image_path))[0]
                output_path = f"watercolor_{base_name}.png"
            
            generated_image.save(output_path)
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            print(f"✅ Generated watercolor illustration!")
            print(f"   Processing time: {processing_time:.1f} seconds")
            print(f"   Saved as: {output_path}")
            
            return output_path
            
        except Exception as e:
            print(f"❌ Error during generation: {e}")
            return None
    
    def post_process_watercolor(self, image):
        """Apply post-processing for watercolor effect"""
        # Slightly reduce saturation for pastel effect
        enhancer = ImageEnhance.Color(image)
        image = enhancer.enhance(0.9)
        
        # Slightly reduce contrast for softer look
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(0.95)
        
        return image

def main():
    """Test the minimal watercolor pipeline"""
    pipeline = MinimalWatercolorPipeline()
    
    # Test images to try
    test_images = [
        "style_photos/before.png",
        "1.png",
        "2.png"
    ]
    
    print("🚀 Testing Minimal Watercolor Pipeline\n")
    
    for image_path in test_images:
        if os.path.exists(image_path):
            print(f"\n📸 Processing: {image_path}")
            result = pipeline.generate_watercolor(image_path, strength=0.7)
            
            if result:
                print(f"✅ Success! Check {result}")
            else:
                print("❌ Failed to generate watercolor")
            
            break  # Only process first available image for testing
        else:
            print(f"⚠️ Image not found: {image_path}")
    
    print("\n🎉 Test complete!")

if __name__ == "__main__":
    main()
