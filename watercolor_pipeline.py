"""
Watercolor Illustration Pipeline
Transforms photos into gentle watercolor illustrations with dot eyes and soft linework
"""

import torch
import numpy as np
from PIL import Image, ImageEnhance
import cv2
import requests
from io import BytesIO

class WatercolorPipeline:
    def __init__(self, device="cuda" if torch.cuda.is_available() else "cpu"):
        self.device = device
        self.pipeline = None
        print(f"🔧 Initializing pipeline on {device}")

    def setup_models(self):
        """Initialize models - simplified version for CPU"""
        print("Loading models...")

        try:
            # Use a lighter model for CPU testing
            from diffusers import StableDiffusionImg2ImgPipeline

            # Load a smaller, faster model for testing
            model_id = "runwayml/stable-diffusion-v1-5"

            self.pipeline = StableDiffusionImg2ImgPipeline.from_pretrained(
                model_id,
                torch_dtype=torch.float32 if self.device == "cpu" else torch.float16,
                safety_checker=None,
                requires_safety_checker=False
            )

            if self.device == "cuda":
                self.pipeline = self.pipeline.to(self.device)
            else:
                # CPU optimizations
                self.pipeline.enable_attention_slicing()

            print("✅ Models loaded successfully!")
            return True

        except Exception as e:
            print(f"❌ Error loading models: {e}")
            return False
    
    def preprocess_image(self, image, target_size=1024):
        """Preprocess input image for optimal results"""
        # Resize while maintaining aspect ratio
        image = self.resize_image(image, target_size)
        
        # Enhance image quality
        enhancer = ImageEnhance.Contrast(image)
        image = enhancer.enhance(1.1)
        
        enhancer = ImageEnhance.Color(image)
        image = enhancer.enhance(1.05)
        
        return image
    
    def resize_image(self, image, target_size):
        """Resize image to target size while maintaining aspect ratio"""
        w, h = image.size
        if w > h:
            new_w = target_size
            new_h = int(h * target_size / w)
        else:
            new_h = target_size
            new_w = int(w * target_size / h)
        
        # Ensure dimensions are multiples of 8 (required for SDXL)
        new_w = (new_w // 8) * 8
        new_h = (new_h // 8) * 8
        
        return image.resize((new_w, new_h), Image.Resampling.LANCZOS)
    
    def create_control_image(self, image):
        """Create edge detection for structure preservation"""
        # Convert to numpy array
        image_np = np.array(image)

        # Convert to grayscale
        gray = cv2.cvtColor(image_np, cv2.COLOR_RGB2GRAY)

        # Apply Canny edge detection with soft settings for gentle lines
        edges = cv2.Canny(gray, 50, 100)

        # Convert back to PIL Image
        edge_image = Image.fromarray(edges).convert('RGB')

        return edge_image

    def generate_watercolor(self, image, prompt_addition="", strength=0.7):
        """Generate watercolor illustration from input image"""

        if self.pipeline is None:
            if not self.setup_models():
                raise RuntimeError("Failed to load models")

        # Preprocess image
        processed_image = self.preprocess_image(image)

        # Craft prompt for watercolor style
        base_prompt = "watercolor painting, gentle brush strokes, soft linework, dot eyes, pastel colors, paper texture, romantic dreamy style, delicate watercolor washes, artistic illustration"

        if prompt_addition:
            prompt = f"{base_prompt}, {prompt_addition}"
        else:
            prompt = base_prompt

        negative_prompt = "harsh lines, detailed eyes, photorealistic, digital art, cartoon, anime, sharp edges, high contrast, dark colors, ugly, blurry"

        # Generate image
        try:
            if self.device == "cuda":
                with torch.autocast("cuda"):
                    result = self.pipeline(
                        prompt=prompt,
                        negative_prompt=negative_prompt,
                        image=processed_image,
                        num_inference_steps=20,  # Reduced for speed
                        guidance_scale=7.5,
                        strength=strength
                    )
            else:
                # CPU generation
                result = self.pipeline(
                    prompt=prompt,
                    negative_prompt=negative_prompt,
                    image=processed_image,
                    num_inference_steps=15,  # Further reduced for CPU
                    guidance_scale=7.5,
                    strength=strength
                )
        except Exception as e:
            print(f"❌ Error during generation: {e}")
            return None

        return result.images[0]
    
    def post_process_for_style(self, image):
        """Apply post-processing to match reference style"""
        # Convert to numpy for processing
        img_np = np.array(image)
        
        # Apply subtle blur for softer look
        img_np = cv2.GaussianBlur(img_np, (3, 3), 0.5)
        
        # Reduce saturation slightly for pastel effect
        img_pil = Image.fromarray(img_np)
        enhancer = ImageEnhance.Color(img_pil)
        img_pil = enhancer.enhance(0.85)
        
        # Add subtle paper texture (simulated)
        img_pil = self.add_paper_texture(img_pil)
        
        return img_pil
    
    def add_paper_texture(self, image):
        """Add subtle paper texture overlay"""
        # Create subtle noise pattern
        w, h = image.size
        noise = np.random.normal(0, 5, (h, w, 3)).astype(np.uint8)
        noise_img = Image.fromarray(noise, 'RGB')
        
        # Blend with original image
        return Image.blend(image, noise_img, 0.03)
    
    def process_image(self, input_image, prompt_addition="", strength=0.7):
        """Main processing function"""
        print("🎨 Starting watercolor transformation...")
        
        # Generate watercolor
        watercolor_image = self.generate_watercolor(input_image, prompt_addition, strength)
        
        # Apply post-processing
        final_image = self.post_process_for_style(watercolor_image)
        
        print("✅ Watercolor transformation complete!")
        return final_image

def load_image_from_url(url):
    """Load image from URL"""
    response = requests.get(url)
    return Image.open(BytesIO(response.content)).convert('RGB')

def load_image_from_path(path):
    """Load image from local path"""
    return Image.open(path).convert('RGB')

# Example usage
if __name__ == "__main__":
    # Initialize pipeline
    pipeline = WatercolorPipeline()
    
    # Load test image (replace with your image path)
    # input_image = load_image_from_path("style_photos/before.png")
    
    # Process image
    # result = pipeline.process_image(input_image)
    
    # Save result
    # result.save("output_watercolor.png")
    
    print("Pipeline ready! Use pipeline.process_image(your_image) to transform photos.")
