"""
Test script for the watercolor pipeline
"""

import os
import time
from watercolor_pipeline import Water<PERSON><PERSON><PERSON>elin<PERSON>, load_image_from_path
from PIL import Image

def test_pipeline():
    """Test the watercolor pipeline with your reference images"""
    
    print("🚀 Initializing Watercolor Pipeline...")
    pipeline = WatercolorPipeline()
    
    # Test images
    test_images = [
        "style_photos/before.png",
        "style_photos/before_without_background.png"
    ]
    
    for i, image_path in enumerate(test_images):
        if not os.path.exists(image_path):
            print(f"⚠️ Image not found: {image_path}")
            continue
            
        print(f"\n📸 Processing image {i+1}: {image_path}")
        
        try:
            # Load image
            input_image = load_image_from_path(image_path)
            print(f"   Original size: {input_image.size}")
            
            # Start timing
            start_time = time.time()
            
            # Process with different strengths to test
            strengths = [0.6, 0.7, 0.8]
            
            for strength in strengths:
                print(f"   🎨 Generating with strength {strength}...")
                
                # Process image
                result = pipeline.process_image(
                    input_image, 
                    prompt_addition="bride, wedding, romantic scene",
                    strength=strength
                )
                
                # Save result
                output_name = f"output_{i+1}_strength_{strength}.png"
                result.save(output_name)
                print(f"   ✅ Saved: {output_name}")
            
            # Calculate processing time
            end_time = time.time()
            processing_time = end_time - start_time
            print(f"   ⏱️ Total processing time: {processing_time:.2f} seconds")
            
        except Exception as e:
            print(f"   ❌ Error processing {image_path}: {e}")
            import traceback
            traceback.print_exc()
    
    print("\n🎉 Pipeline testing complete!")
    print("Check the output files to see the results.")

def quick_test():
    """Quick test with a single image"""
    print("🚀 Quick test...")
    
    pipeline = WatercolorPipeline()
    
    # Use the before.png image
    if os.path.exists("style_photos/before.png"):
        input_image = load_image_from_path("style_photos/before.png")
        
        print("🎨 Processing...")
        start_time = time.time()
        
        result = pipeline.process_image(
            input_image,
            prompt_addition="bride, wedding dress, romantic",
            strength=0.7
        )
        
        end_time = time.time()
        
        result.save("quick_test_output.png")
        print(f"✅ Done! Processing time: {end_time - start_time:.2f}s")
        print("Output saved as: quick_test_output.png")
    else:
        print("❌ Test image not found: style_photos/before.png")

if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "quick":
        quick_test()
    else:
        test_pipeline()
