# CLAUDE.md


## Standard Workflow
1. First think through the problem, read the codebase for relevant files, and write a plan to tasks/todo.md.

2. The plan should have a list of todo items that you can check off as you complete them

3. Before you begin working, check in with me and I will verify the plan.

4. Then, begin working on the todo items, marking them as complete as you go.

5. Please every step of the way just give me a high level explanation of what changes you made

6. Make every task and code change you do as simple as possible. We want to avoid making any massive or complex changes. Every change should impact as little code as possible. Everything is about simplicity.

7. Finally, add a review section to the [todo.md] file with a summary of the changes you made and any other relevant information.


Security prompt:

Please check through all the code you just wrote and make sure it follows security best practices. make sure there are no sensitive information in the front and and there are no vulnerabilities that can be exploited

Learning from Claude prompt:

Please explain the functionality and code you just built out in detail. Walk me through wehat you changed and how it works. Act like you’re a senior engineer teaching me code

## Project Overview

Illustrations V2 is a full-stack e-commerce application that transforms photos into artistic pencil and watercolor illustrations. Built with Next.js 15, Firebase Functions, Supabase, and Stripe.

## MCP Servers Available

This project has the following MCP (Model Context Protocol) servers configured:

### Active MCP Servers

1. **Supabase** (`supabase`)
   - Database operations (read/write access)
   - Schema management and migrations
   - TypeScript type generation
   - SQL query execution
   - Project Reference: `conbzuqhxovendvgdqbi`

2. **Stripe** (`stripe`)
   - Payment processing and management
   - Customer operations
   - Product and pricing management
   - Subscription handling
   - All Stripe tools enabled

3. **GitHub** (`github`)
   - Repository management
   - Branch creation and management
   - Pull request operations
   - CI/CD workflow assistance
   - Code review tools

4. **Context7** (`context7`)
   - Version-specific documentation lookup
   - Code examples for libraries
   - Best practices and patterns
   - Framework-specific guidance

5. **Sequential Thinking** (`sequential-thinking`)
   - Complex problem breakdown
   - Step-by-step planning
   - Systematic analysis
   - Decision-making assistance

### Intermittent MCP Server

- **Effect** (`effect`) - Functional programming utilities
  - ⚠️ Note: This server uses Docker and may fail to connect due to WSL/Docker permission issues
  - If needed, install Effect.js directly: `npm install effect`

### Using MCP Servers

In Claude Code, type `/mcp` to see the status of all connected servers. You can use natural language to interact with these services:

**Examples:**
- "Show me all tables in my Supabase database"
- "Create a Stripe product for premium illustrations"
- "Create a feature branch for adding user avatars"
- "Find the latest Next.js 15 app router documentation"
- "Help me plan the database schema for user profiles step by step"

## Essential Commands

### Frontend Development
```bash
# Start development server
npm run dev

# Build for production
npm run build

# Run linting
npm run lint
```

### Firebase Functions Development
```bash
cd firebase/functions

# Start local emulators
npm run serve

# Deploy to production
npm run deploy

# Build TypeScript
npm run build

# View logs
npm run logs
```

### Docker Management (for MCP servers)
```bash
# Start Docker daemon (if not running)
sudo service docker start

# Check Docker status
sudo service docker status
```

## Architecture Overview

### Tech Stack
- **Frontend**: Next.js 15 (App Router), React 19, TypeScript, Tailwind CSS, Shadcn UI
- **UI Icons**: Lucide React (v0.511.0) - Modern icon library
- **Backend**: Firebase Functions (Node.js 18), Supabase (PostgreSQL), Stripe
- **Services**: Resend (email), Sharp/PDFKit/Puppeteer (image processing)
- **Development**: Claude Code with MCP servers for enhanced AI assistance

### Key Architecture Patterns

1. **API Routes Structure**:
   - Next.js API routes in `src/app/api/` handle client requests
   - Firebase Functions in `firebase/functions/src/` handle heavy processing
   - Stripe webhooks trigger order processing

2. **Payment Flow**:
   - Client uploads photos → temporary Supabase storage
   - Stripe Checkout session created based on photo count
   - Webhook triggers Firebase Function for processing
   - Completed illustrations emailed via Resend

3. **Database Schema**:
   - `orders` table with comprehensive tracking
   - JSON columns for metadata and file arrays
   - Row Level Security policies enabled
   - Managed via Supabase MCP for migrations

4. **File Processing Pipeline**:
   - Upload validation with file type/size checks
   - Temporary storage before payment
   - Processing triggered by successful payment
   - Cleanup scheduled for old temp files

## Development Guidelines

### Type Safety
- TypeScript strict mode is enabled
- Always define proper types for API responses and database queries
- Use Zod schemas for runtime validation
- Generate types from Supabase schema using MCP

### API Development
- Use proper error handling with try-catch blocks
- Return consistent response formats
- Validate all inputs with Zod schemas
- Handle Stripe webhook signatures

### Component Development
- Follow existing Shadcn UI patterns
- Use Tailwind CSS classes for styling
- Implement loading states and error boundaries
- Keep components in appropriate directories

### UI Icons with Lucide React
The project uses Lucide React (v0.511.0) for consistent and modern iconography. Lucide provides:
- Tree-shakable imports for optimal bundle size
- Consistent 24x24 viewBox for all icons
- Customizable size, color, and stroke width
- TypeScript support out of the box

**Currently Used Icons:**
- **Navigation & Actions**: ArrowRight, Menu, X
- **Features & Benefits**: Sparkles, UserCheck, Zap, Heart, Gift
- **Status & Feedback**: Check, CheckCircle, Star, Clock, Frown
- **E-commerce**: Upload, CreditCard, Mail

**Usage Example:**
```tsx
import { Sparkles, ArrowRight } from "lucide-react"

// Basic usage
<Sparkles className="w-4 h-4" />

// With custom styling
<ArrowRight className="w-5 h-5 text-primary animate-pulse" />

// In buttons
<Button>
  Get Started <ArrowRight className="ml-2 w-4 h-4" />
</Button>
```

**Adding New Icons:**
1. Import from `lucide-react` package
2. Use consistent sizing (typically w-4 h-4 or w-5 h-5)
3. Apply theme colors via Tailwind classes
4. Consider accessibility with proper ARIA labels when needed

### Testing Approach
- Manual testing via test pages (`/test`, `/test-order`)
- Test API endpoints available for development
- No unit tests currently implemented

## Key Files and Locations

- **Stripe Products**: `STRIPE_PRODUCTS.md` - pricing tiers configuration
- **Database Schema**: `supabase-schema.sql`
- **Environment Variables**: `.env.local` (create from README.md template)
- **Firebase Config**: `firebase.json`, `firebase/functions/.runtimeconfig.json`
- **MCP Configuration**: `~/.claude.json` (user-level MCP servers)

## Common Development Tasks

### Adding New API Endpoints
1. Create route in `src/app/api/[endpoint]/route.ts`
2. Define Zod schema for validation
3. Implement proper error handling
4. Test with manual requests

### Modifying Order Processing
1. Update Firebase Function in `firebase/functions/src/processors/`
2. Test locally with emulators
3. Deploy with `npm run deploy`

### Updating Email Templates
- Email service in `firebase/functions/src/services/email.ts`
- Use Resend API for sending
- Test with development API key

### Database Operations
- Use Supabase MCP for schema changes
- Example: "Create a new table for user favorites with columns for user_id and illustration_id"
- Generate TypeScript types: "Generate TypeScript types for all database tables"

### Payment Management
- Use Stripe MCP for product/price management
- Example: "Create a new pricing tier for bulk orders"
- Monitor subscriptions: "Show me active subscriptions from the last week"

## Current Status Notes

The project is functionally complete but missing:
- Actual AI/image processing logic (currently placeholder)
- Production deployment configuration
- Comprehensive error handling
- Responsive design polish

Test endpoints should be removed before production deployment.

## Deployment Checklist

A comprehensive production deployment checklist is available in the project documentation, covering:
- Supabase production setup
- Stripe configuration and webhooks
- Environment variables
- Vercel deployment
- Database optimization
- Monitoring setup

Refer to the deployment checklist for detailed steps when ready to deploy to production.