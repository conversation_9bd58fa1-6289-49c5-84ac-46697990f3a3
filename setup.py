"""
Setup script for the watercolor pipeline
"""

import subprocess
import sys
import os

def install_requirements():
    """Install required packages"""
    print("📦 Installing requirements...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ Requirements installed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing requirements: {e}")
        return False
    
    return True

def check_gpu():
    """Check if GPU is available"""
    try:
        import torch
        if torch.cuda.is_available():
            gpu_name = torch.cuda.get_device_name(0)
            print(f"✅ GPU available: {gpu_name}")
            print(f"   CUDA version: {torch.version.cuda}")
            return True
        else:
            print("⚠️ No GPU available, will use CPU (slower)")
            return False
    except ImportError:
        print("❌ PyTorch not installed")
        return False

def setup_environment():
    """Set up the development environment"""
    print("🔧 Setting up watercolor pipeline environment...")
    
    # Install requirements
    if not install_requirements():
        return False
    
    # Check GPU
    gpu_available = check_gpu()
    
    # Create output directory
    os.makedirs("outputs", exist_ok=True)
    print("📁 Created outputs directory")
    
    print("\n🎉 Setup complete!")
    
    if gpu_available:
        print("💡 You can now run: python test_pipeline.py")
    else:
        print("💡 You can run on CPU (slower): python test_pipeline.py")
        print("   Consider using Google Colab or a GPU instance for faster processing")
    
    return True

if __name__ == "__main__":
    setup_environment()
